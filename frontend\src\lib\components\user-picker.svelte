<script lang="ts">
  import type { User, Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";

  interface Props {
    locale: Common.WebsiteLocale;
    selectedUserId: string | null;
    label?: string;
    placeholder?: string;
  }

  const i18n = {
    en: {
      user: "Person",
      selectUser: "Select person",
      searchUsers: "Search people...",
      noUsersFound: "People not found",
      loading: "Loading...",
      clearSelection: "Clear selection",
    },
    ru: {
      user: "Человек",
      selectUser: "Выбрать человека",
      searchUsers: "Поиск людей...",
      noUsersFound: "Люди не найдены",
      loading: "Загрузка...",
      clearSelection: "Очистить выбор",
    },
  };

  const { fetcher: api } = getClient();

  let { selectedUserId = $bindable(), locale, label, placeholder }: Props = $props();

  const t = $derived(i18n[locale]);

  // State
  let showDropdown = $state(false);
  let userSearchQuery = $state("");
  let searchResults = $state<User.GetUsersOutput | null>(null);
  let selectedUser = $state<User.GetUserOutput | null>(null);
  let isSearching = $state(false);
  let searchDebounceTimeout = $state<ReturnType<typeof setTimeout> | null>(null);
  let inputElement: HTMLInputElement;

  // Helper function to get appropriate localization
  function getAppropriateLocalization(localizations: Common.Localizations): string {
    const localization = localizations.find((l) => l.locale === locale);
    return localization?.value || localizations[0]?.value || "";
  }

  // Load selected user on mount and when selectedUserId changes
  $effect(() => {
    if (selectedUserId) {
      loadSelectedUser();
    } else {
      selectedUser = null;
    }
  });

  // Load users when dropdown is shown (even without search query)
  $effect(() => {
    if (showDropdown && !searchResults) {
      searchUsers("");
    }
  });

  async function loadSelectedUser() {
    if (!selectedUserId) return;

    if (selectedUserId === selectedUser?.id) {
      return;
    }

    try {
      const users = await api.user.list.get({ ids: [selectedUserId] });
      selectedUser = users[0] || null;
    } catch (error) {
      console.error("Failed to load selected user:", error);
      selectedUser = null;
    }
  }

  async function searchUsers(query: string) {
    const searchQuery = query.trim() || undefined;

    isSearching = true;

    try {
      searchResults = await api.user.list.get({ query: searchQuery });
    } catch (error) {
      console.error("Failed to search users:", error);
      searchResults = [];
    } finally {
      isSearching = false;
    }
  }

  function debounceSearch(query: string) {
    if (searchDebounceTimeout) {
      clearTimeout(searchDebounceTimeout);
    }

    searchDebounceTimeout = setTimeout(() => {
      searchUsers(query);
    }, 300);
  }

  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    userSearchQuery = target.value;
    debounceSearch(userSearchQuery);
  }

  function selectUser(user: User.GetUserOutput) {
    selectedUserId = user.id;
    selectedUser = user;

    // Clear search and hide dropdown
    userSearchQuery = "";
    showDropdown = false;
    inputElement?.blur();
  }

  function clearSelection() {
    selectedUserId = null;
    selectedUser = null;
  }

  function handleInputFocus() {
    showDropdown = true;
  }

  function handleInputBlur() {
    // Use setTimeout to allow click events on dropdown items to fire first
    setTimeout(() => {
      showDropdown = false;
    }, 150);
  }
</script>

<div class="mb-3">
  {#if label}
    <label for="user-search-input" class="form-label">{label}</label>
  {/if}

  <div class="user-picker">
    <!-- Selected User Display -->
    {#if selectedUser}
      <div class="selected-user d-flex align-items-center gap-2 mb-2">
        <div class="d-flex align-items-center bg-light border rounded p-2 flex-grow-1">
          {#if selectedUser.image}
            <img
              src={`/images/${selectedUser.image}`}
              alt={getAppropriateLocalization(selectedUser.name)}
              class="rounded me-2"
              style="width: 32px; height: 32px; object-fit: cover;"
            />
          {:else}
            <img
              src={`/images/default-avatar.png`}
              alt={getAppropriateLocalization(selectedUser.name)}
              class="rounded-circle me-2"
              style="width: 32px; height: 32px; object-fit: cover;"
            />
          {/if}
          <div class="flex-grow-1">
            <div class="fw-medium">{getAppropriateLocalization(selectedUser.name)}</div>
          </div>
          <button
            type="button"
            class="btn btn-sm btn-outline-danger"
            onclick={clearSelection}
            aria-label={t.clearSelection}
          >
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>
    {/if}

    <!-- User Search Input -->
    <div class="user-search-container position-relative">
      <input
        bind:this={inputElement}
        type="text"
        id="user-search-input"
        class="form-control"
        placeholder={placeholder || t.searchUsers}
        autocomplete="off"
        bind:value={userSearchQuery}
        oninput={handleSearchInput}
        onfocus={handleInputFocus}
        onblur={handleInputBlur}
      />

      <!-- Search Results Dropdown -->
      {#if showDropdown && searchResults && (isSearching || searchResults.length > 0)}
        <div
          class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm"
          style="z-index: 1000; max-height: 200px; overflow-y: auto;"
        >
          {#if isSearching}
            <div class="p-2 text-muted">
              <i class="bi bi-hourglass-split me-1"></i>
              {t.loading}
            </div>
          {:else if searchResults.length === 0}
            <div class="p-2 text-muted">
              {t.noUsersFound}
            </div>
          {:else}
            {#each searchResults as user (user.id)}
              <button
                type="button"
                class="dropdown-item d-flex align-items-center p-2"
                onclick={() => selectUser(user)}
              >
                {#if user.image}
                  <img
                    src={`/images/${user.image}`}
                    alt={getAppropriateLocalization(user.name)}
                    class="rounded me-2"
                    style="width: 24px; height: 24px; object-fit: cover;"
                  />
                {:else}
                  <img
                    src={`/images/default-avatar.png`}
                    alt={getAppropriateLocalization(user.name)}
                    class="rounded-circle me-2"
                    style="width: 24px; height: 24px; object-fit: cover;"
                  />
                {/if}
                <div>
                  <div class="fw-medium">{getAppropriateLocalization(user.name)}</div>
                </div>
              </button>
            {/each}
          {/if}
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .user-picker {
    position: relative;
  }

  .selected-user {
    min-height: 2rem;
  }

  .search-results {
    border-top: none !important;
  }

  .search-results .dropdown-item {
    border: none;
    background: none;
    text-align: left;
    width: 100%;
  }

  .search-results .dropdown-item:hover {
    background-color: var(--bs-light);
  }
</style>
