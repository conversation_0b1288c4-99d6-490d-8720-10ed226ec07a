<script lang="ts">
  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import CreateCommuneModal from "./create-commune-modal.svelte";
  import { getPlural } from "$lib";

  const i18n = {
    en: {
      _page: {
        title: "Communes — Commune",
      },
      communes: "Communes",
      create: "Create",
      invitations: "Invitations",
      joinRequests: "Join Requests",
      myInvitations: "My Invitations",
      myJoinRequests: "My Join Requests",
      loading: "Loading...",
      noCommunes: "No communes found",
      membersCount(count: number) {
        switch (getPlural.en(count)) {
          case 0:
            return `${count} member`;
          case 1:
            return `${count} members`;
        }
      },
      headMember: "Head",
      errorFetchingCommunes: "Failed to fetch communes",
      errorOccurred: "An error occurred while fetching communes",
      first: "First",
      previous: "Previous",
      next: "Next",
      last: "Last",
      page: "Page",
      loadingMore: "Loading more communes...",
      noImage: "No image",
      communeImageAlt: "Commune image",
    },
    ru: {
      _page: {
        title: "Коммуны — Коммуна",
      },
      communes: "Коммуны",
      create: "Создать",
      invitations: "Приглашения",
      joinRequests: "Заявки",
      myInvitations: "Мои приглашения",
      myJoinRequests: "Мои заявки",
      loading: "Загрузка...",
      noCommunes: "Коммуны не найдены",
      membersCount(count: number) {
        switch (getPlural.ru(count)) {
          case 0:
            return `${count} участник`;
          case 1:
            return `${count} участника`;
          case 2:
            return `${count} участников`;
        }
      },
      headMember: "Глава",
      errorFetchingCommunes: "Не удалось загрузить коммуны",
      errorOccurred: "Произошла ошибка при загрузке коммун",
      first: "Первая",
      previous: "Предыдущая",
      next: "Следующая",
      last: "Последняя",
      page: "Страница",
      loadingMore: "Загружаем больше коммун...",
      noImage: "Нет изображения",
      communeImageAlt: "Изображение коммуны",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let communes = $state(data.communes);
  let error = $state<string | null>(null);
  let showCreateModal = $state(false);

  let isLoadingMore = $state(false);

  let currentPage = $state(1);
  let isHasMoreCommunes = $state(data.isHasMoreCommunes);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Function to load more communes
  async function loadMoreCommunes() {
    if (isLoadingMore || !isHasMoreCommunes) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newCommunes = await api.commune.list.get({
        pagination: {
          page: nextPage,
        },
      });

      communes = [...communes, ...newCommunes];
      currentPage = nextPage;

      isHasMoreCommunes = newCommunes.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Modal handlers
  function handleCreateModalOpen() {
    showCreateModal = true;
  }

  function handleCreateModalClose() {
    showCreateModal = false;
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMoreCommunes && !isLoadingMore) {
            loadMoreCommunes();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4 gap-2 flex-wrap">
    <h1 class="mb-0 flex-shrink-0">{t.communes}</h1>
    <div class="d-flex align-items-center gap-1 flex-wrap">
      {#if data.isLoggedIn}
        <!-- Navigation links for logged-in users -->
        <a
          href={toLocaleHref("/communes/invitations")}
          class="btn btn-outline-info position-relative btn-sm"
        >
          <span class="d-none d-lg-inline">{t.myInvitations}</span>
          <span class="d-lg-none">{t.invitations}</span>
          {#if data.pendingInvitationsCount > 0}
            <span
              class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
            >
              {data.pendingInvitationsCount}
              <span class="visually-hidden">pending invitations</span>
            </span>
          {/if}
        </a>
        <a href={toLocaleHref("/communes/join-requests")} class="btn btn-outline-secondary btn-sm">
          <span class="d-none d-lg-inline">{t.myJoinRequests}</span>
          <span class="d-lg-none">{t.joinRequests}</span>
        </a>
      {/if}
      <button class="btn btn-primary btn-sm" onclick={handleCreateModalOpen}>
        <i class="bi bi-plus-circle me-1"></i>
        {t.create}
      </button>
    </div>
  </div>

  {#if communes.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noCommunes}</p>
    </div>
  {:else}
    <div class="row row-cols-1 row-cols-sm-2 row-cols-md-3 row-cols-lg-5 g-4">
      {#each communes as commune (commune.id)}
        <div class="col">
          <div class="card h-100 shadow-sm hover-card">
            <a
              href={toLocaleHref(`/communes/${commune.id}`)}
              class="text-decoration-none text-black"
            >
              {#if commune.image}
                <div class="image-container">
                  <img src={`/images/${commune.image}`} alt={`${t.communeImageAlt}`} />
                </div>
              {:else}
                <div
                  class="bg-light text-center d-flex align-items-center justify-content-center"
                  style="height: 140px;"
                >
                  <span class="text-muted">{t.noImage}</span>
                </div>
              {/if}

              <div class="card-body d-flex flex-column">
                <h5 class="card-title fs-5 text-truncate">
                  {getAppropriateLocalization(commune.name)}
                </h5>

                <p class="card-text text-muted small" style="height: 3rem; overflow: hidden">
                  {getAppropriateLocalization(commune.description) || ""}
                </p>

                <div class="mt-auto">
                  <span class="badge bg-primary mb-2">
                    {t.membersCount(commune.memberCount)}
                  </span>

                  <div class="small text-muted">
                    <div>{t.headMember}:</div>
                    <div class="d-flex flex-column">
                      {getAppropriateLocalization(commune.headMember.name)}
                    </div>
                  </div>
                </div>
              </div>
            </a>
          </div>
        </div>
      {/each}
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreCommunes}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}

  <!-- Create Commune Modal -->
  <CreateCommuneModal
    {locale}
    show={showCreateModal}
    onHide={handleCreateModalClose}
    {toLocaleHref}
  />
</div>

<style lang="scss">
  .hover-card {
    cursor: pointer;
    transition: transform 0.2s;
  }

  .hover-card:hover {
    transform: translateY(-5px);
  }

  .image-container {
    width: 100%;
    // height: 100%;
    height: 140px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  /* Header button responsiveness */
  @media (max-width: 991.98px) {
    .btn-sm {
      font-size: 0.8rem;
      padding: 0.25rem 0.5rem;
    }
  }

  @media (max-width: 767.98px) {
    .btn-sm {
      font-size: 0.75rem;
      padding: 0.2rem 0.4rem;
    }
  }

  @media (max-width: 575.98px) {
    .d-flex.justify-content-between {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 1rem;
    }

    .d-flex.align-items-center.gap-1 {
      width: 100%;
      justify-content: flex-start;
    }
  }
</style>
