<script lang="ts">
  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { HttpError } from "@commune/api/acrpc/client";
  import { formatDate } from "$lib";
  import { getClient } from "$lib/acrpc";
  import { goto } from "$app/navigation";

  const i18n = {
    en: {
      _page: {
        title: "Commune Invitations — Commune",
      },
      invitations: "Commune Invitations",
      loading: "Loading...",
      noInvitations: "No invitations found",
      member: "member",
      members: "members",
      headMember: "Head",
      errorFetchingInvitations: "Failed to fetch invitations",
      errorOccurred: "An error occurred while fetching invitations",
      loadingMore: "Loading more invitations...",
      accept: "Accept",
      reject: "Reject",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      expired: "Expired",
      invitedOn: "Invited on",
      acceptingInvitation: "Accepting...",
      rejectingInvitation: "Rejecting...",
      errorAcceptingInvitation: "Failed to accept invitation",
      errorRejectingInvitation: "Failed to reject invitation",
      invitationAccepted: "Invitation accepted! Redirecting to commune...",
      backToCommunes: "Back to Communes",
      viewCommune: "View Commune",
      noImage: "No image",
      communeImageAlt: "Commune image",
      youReachedMaxCommunesLimit: `You have reached the maximum number of communes where you are member (${Consts.MAX_COMMUNES_TO_BE_MEMBER}).`,
    },
    ru: {
      _page: {
        title: "Приглашения в коммуны — Коммуна",
      },
      invitations: "Приглашения в коммуны",
      loading: "Загрузка...",
      noInvitations: "Приглашения не найдены",
      member: "участник",
      members: "участников",
      headMember: "Глава",
      errorFetchingInvitations: "Не удалось загрузить приглашения",
      errorOccurred: "Произошла ошибка при загрузке приглашений",
      loadingMore: "Загружаем больше приглашений...",
      accept: "Принять",
      reject: "Отклонить",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      expired: "Истекло",
      invitedOn: "Приглашен",
      acceptingInvitation: "Принимаем...",
      rejectingInvitation: "Отклоняем...",
      errorAcceptingInvitation: "Не удалось принять приглашение",
      errorRejectingInvitation: "Не удалось отклонить приглашение",
      invitationAccepted: "Приглашение принято! Перенаправляем в коммуну...",
      backToCommunes: "Назад к коммунам",
      viewCommune: "Посмотреть коммуну",
      noImage: "Нет изображения",
      communeImageAlt: "Изображение коммуны",
      youReachedMaxCommunesLimit: `Вы достигли максимального количества коммун, где вы являетесь участником (${Consts.MAX_COMMUNES_TO_BE_MEMBER}).`,
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let invitations = $state(data.invitations);
  let error = $state<string | null>(null);

  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreInvitations = $state(data.isHasMoreInvitations);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Track loading states for individual invitations
  let loadingStates = $state<Record<string, "accepting" | "rejecting" | null>>({});

  // Function to load more invitations
  async function loadMoreInvitations() {
    if (isLoadingMore || !isHasMoreInvitations) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newInvitations = await api.commune.invitation.list.get({
        pagination: { page: nextPage },
      });

      const communes = newInvitations.length
        ? await api.commune.list.get({
            ids: newInvitations.map((invitation) => invitation.communeId),
          })
        : [];
      const communeMap = new Map(communes.map((commune) => [commune.id, commune]));

      const invitationsWithDetails = newInvitations.map((invitation) => ({
        ...invitation,
        commune: communeMap.get(invitation.communeId)!,
      }));

      invitations = [...invitations, ...invitationsWithDetails];
      currentPage = nextPage;

      // Check if there are more invitations to load
      isHasMoreInvitations = newInvitations.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Function to accept an invitation
  async function acceptInvitation(invitationId: string, communeId: string) {
    loadingStates[invitationId] = "accepting";
    error = null;

    try {
      await api.commune.invitation.accept.post({ id: invitationId });

      alert(t.invitationAccepted);
      goto(toLocaleHref(`/communes/${communeId}`));
    } catch (err) {
      if (err instanceof HttpError) {
        if (err.description.includes("user_reached_max_communes_limit")) {
          error = t.youReachedMaxCommunesLimit;

          return;
        }
      }

      error = err instanceof Error ? err.message : t.errorAcceptingInvitation;
      console.error(err);
    } finally {
      loadingStates[invitationId] = null;
    }
  }

  // Function to reject an invitation
  async function rejectInvitation(invitationId: string) {
    loadingStates[invitationId] = "rejecting";
    error = null;

    try {
      await api.commune.invitation.reject.post({ id: invitationId });

      invitations = invitations.map((inv) => {
        if (inv.id === invitationId) {
          return { ...inv, status: "rejected" };
        }

        return inv;
      });
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorRejectingInvitation;
      console.error(err);
    } finally {
      loadingStates[invitationId] = null;
    }
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMoreInvitations && !isLoadingMore) {
            loadMoreInvitations();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });

  // Helper function to get status badge class
  function getStatusBadgeClass(status: string) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      case "expired":
        return "bg-secondary";
      default:
        return "bg-secondary";
    }
  }

  // Helper function to get status text
  function getStatusText(status: string) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      case "expired":
        return t.expired;
      default:
        return status;
    }
  }
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4">
    <h1>{t.invitations}</h1>
    <a href={toLocaleHref("/communes")} class="btn btn-outline-secondary">
      {t.backToCommunes}
    </a>
  </div>

  {#if invitations.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noInvitations}</p>
    </div>
  {:else}
    <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
      {#each invitations as invitation (invitation.id)}
        <div class="col">
          <div class="card h-100 shadow-sm">
            {#if invitation.commune.image}
              <div class="image-container">
                <img src={`/images/${invitation.commune.image}`} alt={`${t.communeImageAlt}`} />
              </div>
            {:else}
              <div
                class="bg-light text-center d-flex align-items-center justify-content-center"
                style="height: 140px;"
              >
                <span class="text-muted">{t.noImage}</span>
              </div>
            {/if}

            <div class="card-body d-flex flex-column">
              <!-- Status badge -->
              <div class="d-flex justify-content-between align-items-start mb-2">
                <span class={`badge ${getStatusBadgeClass(invitation.status)}`}>
                  {getStatusText(invitation.status)}
                </span>
                <small class="text-muted">
                  {t.invitedOn}
                  {formatDate(invitation.createdAt, locale)}
                </small>
              </div>

              <!-- Commune name -->
              <h5 class="card-title fs-5 text-truncate mb-2">
                {getAppropriateLocalization(invitation.commune?.name) || "Unknown Commune"}
              </h5>

              <!-- Commune description -->
              <p class="card-text text-muted small mb-3" style="height: 3rem; overflow: hidden">
                {getAppropriateLocalization(invitation.commune.description) || ""}
              </p>

              <!-- Commune info -->
              <div class="mb-3">
                <span class="badge bg-primary mb-2">
                  {invitation.commune?.memberCount || 0}
                  {(invitation.commune?.memberCount || 0) === 1 ? t.member : t.members}
                </span>

                <div class="small text-muted">
                  <div>{t.headMember}:</div>
                  <div class="d-flex flex-column">
                    {getAppropriateLocalization(invitation.commune.headMember.name) || "Unknown"}
                  </div>
                </div>
              </div>

              <!-- Action buttons for pending invitations -->
              {#if invitation.status === "pending"}
                <div class="mt-auto">
                  <div class="d-grid gap-2">
                    <button
                      class="btn btn-success"
                      disabled={loadingStates[invitation.id] === "accepting"}
                      onclick={() =>
                        acceptInvitation(
                          invitation.id,
                          invitation.commune?.id || invitation.communeId,
                        )}
                    >
                      {#if loadingStates[invitation.id] === "accepting"}
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        {t.acceptingInvitation}
                      {:else}
                        {t.accept}
                      {/if}
                    </button>
                    <button
                      class="btn btn-outline-danger"
                      disabled={loadingStates[invitation.id] === "rejecting"}
                      onclick={() => rejectInvitation(invitation.id)}
                    >
                      {#if loadingStates[invitation.id] === "rejecting"}
                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                        {t.rejectingInvitation}
                      {:else}
                        {t.reject}
                      {/if}
                    </button>
                  </div>
                </div>
              {:else}
                <!-- For non-pending invitations, show link to commune -->
                <div class="mt-auto">
                  <a
                    href={toLocaleHref(
                      `/communes/${invitation.commune?.id || invitation.communeId}`,
                    )}
                    class="btn btn-outline-primary w-100"
                  >
                    {t.viewCommune}
                  </a>
                </div>
              {/if}
            </div>
          </div>
        </div>
      {/each}
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreInvitations}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>

<style lang="scss">
  .image-container {
    width: 100%;
    // height: 100%;
    height: 140px;
    position: relative;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .hover-card {
    cursor: pointer;
    transition: transform 0.2s;
  }

  .hover-card:hover {
    transform: translateY(-5px);
  }
</style>
