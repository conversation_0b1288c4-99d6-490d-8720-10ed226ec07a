<script lang="ts">
  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import { formatDate } from "$lib";
  import { HttpError } from "@commune/api/acrpc/client";

  const i18n = {
    en: {
      _page: {
        title: "Join Requests — Commune",
      },
      communeJoinRequests: "Join Requests",
      loading: "Loading...",
      noJoinRequests: "No join requests found",
      errorFetchingJoinRequests: "Failed to fetch join requests",
      errorOccurred: "An error occurred while fetching join requests",
      loadingMore: "Loading more join requests...",
      accept: "Accept",
      reject: "Reject",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      requestedOn: "Requested on",
      acceptingRequest: "Accepting...",
      rejectingRequest: "Rejecting...",
      errorAcceptingRequest: "Failed to accept join request",
      errorRejectingRequest: "Failed to reject join request",
      requestAccepted: "Join request accepted",
      requestRejected: "Join request rejected",
      backToCommune: "Back to Commune",
      requestingUser: "Requesting Person",
      status: "Status",
      actions: "Actions",
      confirmAccept: "Are you sure you want to accept this join request?",
      confirmReject: "Are you sure you want to reject this join request?",
      userReachedMaxCommunesLimit: `Person has reached the maximum number of communes where he is member (${Consts.MAX_COMMUNES_TO_BE_MEMBER}).`,
    },
    ru: {
      _page: {
        title: "Заявки на вступление — Коммуна",
      },
      communeJoinRequests: "Заявки на вступление",
      loading: "Загрузка...",
      noJoinRequests: "Заявки не найдены",
      errorFetchingJoinRequests: "Не удалось загрузить заявки",
      errorOccurred: "Произошла ошибка при загрузке заявок",
      loadingMore: "Загружаем больше заявок...",
      accept: "Принять",
      reject: "Отклонить",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      requestedOn: "Подана",
      acceptingRequest: "Принимаем...",
      rejectingRequest: "Отклоняем...",
      errorAcceptingRequest: "Не удалось принять заявку",
      errorRejectingRequest: "Не удалось отклонить заявку",
      requestAccepted: "Заявка принята",
      requestRejected: "Заявка отклонена",
      backToCommune: "Назад к коммуне",
      requestingUser: "Человек",
      status: "Статус",
      actions: "Действия",
      confirmAccept: "Вы уверены, что хотите принять эту заявку?",
      confirmReject: "Вы уверены, что хотите отклонить эту заявку?",
      userReachedMaxCommunesLimit: `Человек достиг максимального количества коммун, где он является участником (${Consts.MAX_COMMUNES_TO_BE_MEMBER}).`,
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let joinRequests = $state(data.joinRequests);
  let error = $state<string | null>(null);

  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreJoinRequests = $state(data.isHasMoreJoinRequests);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Track loading states for individual join requests
  let loadingStates = $state<Record<string, "accepting" | "rejecting" | null>>({});

  // Function to load more join requests
  async function loadMoreJoinRequests() {
    if (isLoadingMore || !isHasMoreJoinRequests) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newJoinRequests = await api.commune.joinRequest.list.get({
        pagination: { page: nextPage },
        communeId: data.commune.id,
      });

      const users = newJoinRequests.length
        ? await api.user.list.get({
            ids: newJoinRequests.map((joinRequest) => joinRequest.userId),
          })
        : [];
      const userMap = new Map(users.map((user) => [user.id, user]));

      const joinRequestsWithDetails = newJoinRequests.map((joinRequest) => ({
        ...joinRequest,
        user: userMap.get(joinRequest.userId)!,
      }));

      joinRequests = [...joinRequests, ...joinRequestsWithDetails];
      currentPage = nextPage;

      // Check if there are more join requests to load
      isHasMoreJoinRequests = newJoinRequests.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Function to accept a join request
  async function acceptJoinRequest(joinRequestId: string) {
    if (!confirm(t.confirmAccept)) {
      return;
    }

    loadingStates[joinRequestId] = "accepting";
    error = null;

    try {
      await api.commune.joinRequest.accept.post({ id: joinRequestId });

      // Update the join request status in the list
      joinRequests = joinRequests.map((req) =>
        req.id === joinRequestId ? { ...req, status: "accepted" } : req,
      );

      // Show success message
      alert(t.requestAccepted);
    } catch (err) {
      if (err instanceof HttpError) {
        if (err.description.includes("user_reached_max_communes_limit")) {
          error = t.userReachedMaxCommunesLimit;

          return;
        }
      }

      error = err instanceof Error ? err.message : t.errorAcceptingRequest;
      console.error(err);
    } finally {
      loadingStates[joinRequestId] = null;
    }
  }

  // Function to reject a join request
  async function rejectJoinRequest(joinRequestId: string) {
    if (!confirm(t.confirmReject)) {
      return;
    }

    loadingStates[joinRequestId] = "rejecting";
    error = null;

    try {
      await api.commune.joinRequest.reject.post({ id: joinRequestId });

      // Update the join request status in the list
      joinRequests = joinRequests.map((req) =>
        req.id === joinRequestId ? { ...req, status: "rejected" as const } : req,
      );

      // Show success message
      alert(t.requestRejected);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorRejectingRequest;
      console.error(err);
    } finally {
      loadingStates[joinRequestId] = null;
    }
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMoreJoinRequests && !isLoadingMore) {
            loadMoreJoinRequests();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });

  // Helper function to get status badge class
  function getStatusBadgeClass(status: string) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      default:
        return "bg-secondary";
    }
  }

  // Helper function to get status text
  function getStatusText(status: string) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      default:
        return status;
    }
  }
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4">
    <div>
      <h1>{t.communeJoinRequests}</h1>
      <p class="text-muted mb-0">
        {getAppropriateLocalization(data.commune.name)}
      </p>
    </div>
    <a href={toLocaleHref(`/communes/${data.commune.id}`)} class="btn btn-outline-secondary">
      {t.backToCommune}
    </a>
  </div>

  {#if joinRequests.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noJoinRequests}</p>
    </div>
  {:else}
    <!-- Desktop table view -->
    <div class="d-none d-md-block">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>{t.requestingUser}</th>
              <th>{t.status}</th>
              <th>{t.requestedOn}</th>
              <th>{t.actions}</th>
            </tr>
          </thead>
          <tbody>
            {#each joinRequests as joinRequest (joinRequest.id)}
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    {#if joinRequest.user.image}
                      <img
                        src={`/images/${joinRequest.user.image}`}
                        alt="User avatar"
                        class="rounded me-2"
                        style="width: 32px; height: 32px; object-fit: cover;"
                      />
                    {:else}
                      <div
                        class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2"
                        style="width: 32px; height: 32px;"
                      >
                        <i class="bi bi-person text-white"></i>
                      </div>
                    {/if}
                    <div>
                      <div class="fw-medium">
                        {getAppropriateLocalization(joinRequest.user.name)}
                      </div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class={`badge ${getStatusBadgeClass(joinRequest.status)}`}>
                    {getStatusText(joinRequest.status)}
                  </span>
                </td>
                <td class="text-muted">{formatDate(joinRequest.createdAt, locale)}</td>
                <td>
                  {#if joinRequest.status === "pending"}
                    <div class="d-flex gap-1">
                      <button
                        class="btn btn-sm btn-success"
                        disabled={loadingStates[joinRequest.id] === "accepting"}
                        onclick={() => acceptJoinRequest(joinRequest.id)}
                      >
                        {#if loadingStates[joinRequest.id] === "accepting"}
                          <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                          {t.acceptingRequest}
                        {:else}
                          {t.accept}
                        {/if}
                      </button>
                      <button
                        class="btn btn-sm btn-outline-danger"
                        disabled={loadingStates[joinRequest.id] === "rejecting"}
                        onclick={() => rejectJoinRequest(joinRequest.id)}
                      >
                        {#if loadingStates[joinRequest.id] === "rejecting"}
                          <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                          {t.rejectingRequest}
                        {:else}
                          {t.reject}
                        {/if}
                      </button>
                    </div>
                  {:else}
                    <span class="text-muted">—</span>
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Mobile card view -->
    <div class="d-md-none">
      <div class="row g-3">
        {#each joinRequests as joinRequest (joinRequest.id)}
          <div class="col-12">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-start justify-content-between mb-3">
                  <div class="d-flex align-items-center">
                    {#if joinRequest.user.image}
                      <img
                        src={`/images/${joinRequest.user.image}`}
                        alt="User avatar"
                        class="rounded me-3"
                        style="width: 48px; height: 48px; object-fit: cover;"
                      />
                    {:else}
                      <div
                        class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3"
                        style="width: 48px; height: 48px;"
                      >
                        <i class="bi bi-person text-white"></i>
                      </div>
                    {/if}
                    <div>
                      <div class="fw-medium">
                        {getAppropriateLocalization(joinRequest.user.name)}
                      </div>
                    </div>
                  </div>
                  <span class={`badge ${getStatusBadgeClass(joinRequest.status)}`}>
                    {getStatusText(joinRequest.status)}
                  </span>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                  <small class="text-muted">
                    {t.requestedOn}
                    {formatDate(joinRequest.createdAt, locale)}
                  </small>
                  {#if joinRequest.status === "pending"}
                    <div class="d-flex gap-1">
                      <button
                        class="btn btn-sm btn-success"
                        disabled={loadingStates[joinRequest.id] === "accepting"}
                        onclick={() => acceptJoinRequest(joinRequest.id)}
                      >
                        {#if loadingStates[joinRequest.id] === "accepting"}
                          <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                          {t.acceptingRequest}
                        {:else}
                          {t.accept}
                        {/if}
                      </button>
                      <button
                        class="btn btn-sm btn-outline-danger"
                        disabled={loadingStates[joinRequest.id] === "rejecting"}
                        onclick={() => rejectJoinRequest(joinRequest.id)}
                      >
                        {#if loadingStates[joinRequest.id] === "rejecting"}
                          <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                          {t.rejectingRequest}
                        {:else}
                          {t.reject}
                        {/if}
                      </button>
                    </div>
                  {/if}
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreJoinRequests}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>
