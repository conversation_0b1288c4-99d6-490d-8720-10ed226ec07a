type DynamicRoutes = {
	"/api/[...slug]": { slug: string };
	"/[[locale]]/auth": { locale?: string };
	"/[[locale]]/(index)/communes": { locale?: string };
	"/[[locale]]/(index)/communes/invitations": { locale?: string };
	"/[[locale]]/(index)/communes/join-requests": { locale?: string };
	"/[[locale]]/(index)/communes/[id]": { locale?: string; id: string };
	"/[[locale]]/(index)/communes/[id]/invitations": { locale?: string; id: string };
	"/[[locale]]/(index)/communes/[id]/join-requests": { locale?: string; id: string };
	"/[[locale]]/(index)/layout": { locale?: string };
	"/[[locale]]/(index)/new-calendar": { locale?: string };
	"/[[locale]]/(index)/new-english": { locale?: string };
	"/[[locale]]/(index)/people": { locale?: string };
	"/[[locale]]/(index)/people/[id]": { locale?: string; id: string };
	"/[[locale]]/(index)/people/[id]/feedback": { locale?: string; id: string };
	"/[[locale]]/(index)/people/[id]/karma": { locale?: string; id: string };
	"/[[locale]]/(index)/profile": { locale?: string };
	"/[[locale]]/reactor": { locale?: string };
	"/[[locale]]/reactor/communities": { locale?: string };
	"/[[locale]]/reactor/communities/[id]": { locale?: string; id: string };
	"/[[locale]]/reactor/hubs": { locale?: string };
	"/[[locale]]/reactor/hubs/[id]": { locale?: string; id: string };
	"/[[locale]]/reactor/[id]": { locale?: string; id: string };
	"/[[locale]]/(index)/rules": { locale?: string };
	"/[[locale]]/test": { locale?: string };
	"/[[locale]]/test/editor": { locale?: string };
	"/[[locale]]/test/tag": { locale?: string };
	"/[[locale]]/(index)/the-law": { locale?: string };
	"/[[locale]]/(index)": { locale?: string };
	"/[[locale]]": { locale?: string }
};

type Layouts = {
	"/": { slug?: string; locale?: string; id?: string };
	"/admin": undefined;
	"/admin/invites": undefined;
	"/api": { slug?: string };
	"/api/[...slug]": { slug: string };
	"/[[locale]]/auth": { locale?: string };
	"/[[locale]]/(index)/communes": { locale?: string; id?: string };
	"/[[locale]]/(index)/communes/invitations": { locale?: string };
	"/[[locale]]/(index)/communes/join-requests": { locale?: string };
	"/[[locale]]/(index)/communes/[id]": { locale?: string; id: string };
	"/[[locale]]/(index)/communes/[id]/invitations": { locale?: string; id: string };
	"/[[locale]]/(index)/communes/[id]/join-requests": { locale?: string; id: string };
	"/[[locale]]/(index)/layout": { locale?: string };
	"/[[locale]]/(index)/new-calendar": { locale?: string };
	"/[[locale]]/(index)/new-english": { locale?: string };
	"/[[locale]]/(index)/people": { locale?: string; id?: string };
	"/[[locale]]/(index)/people/[id]": { locale?: string; id: string };
	"/[[locale]]/(index)/people/[id]/feedback": { locale?: string; id: string };
	"/[[locale]]/(index)/people/[id]/karma": { locale?: string; id: string };
	"/[[locale]]/(index)/profile": { locale?: string };
	"/[[locale]]/reactor": { locale?: string; id?: string };
	"/[[locale]]/reactor/communities": { locale?: string; id?: string };
	"/[[locale]]/reactor/communities/[id]": { locale?: string; id: string };
	"/[[locale]]/reactor/hubs": { locale?: string; id?: string };
	"/[[locale]]/reactor/hubs/[id]": { locale?: string; id: string };
	"/[[locale]]/reactor/[id]": { locale?: string; id: string };
	"/robots.txt": undefined;
	"/[[locale]]/(index)/rules": { locale?: string };
	"/sitemap.xml": undefined;
	"/test-image-input": undefined;
	"/[[locale]]/test": { locale?: string };
	"/[[locale]]/test/editor": { locale?: string };
	"/[[locale]]/test/tag": { locale?: string };
	"/[[locale]]/(index)/the-law": { locale?: string };
	"/[[locale]]/(index)": { locale?: string; id?: string };
	"/[[locale]]": { locale?: string; id?: string }
};

export type RouteId = "/" | "/admin" | "/admin/invites" | "/api" | "/api/[...slug]" | "/[[locale]]/auth" | "/[[locale]]/(index)/communes" | "/[[locale]]/(index)/communes/invitations" | "/[[locale]]/(index)/communes/join-requests" | "/[[locale]]/(index)/communes/[id]" | "/[[locale]]/(index)/communes/[id]/invitations" | "/[[locale]]/(index)/communes/[id]/join-requests" | "/[[locale]]/(index)/layout" | "/[[locale]]/(index)/new-calendar" | "/[[locale]]/(index)/new-english" | "/[[locale]]/(index)/people" | "/[[locale]]/(index)/people/[id]" | "/[[locale]]/(index)/people/[id]/feedback" | "/[[locale]]/(index)/people/[id]/karma" | "/[[locale]]/(index)/profile" | "/[[locale]]/reactor" | "/[[locale]]/reactor/communities" | "/[[locale]]/reactor/communities/[id]" | "/[[locale]]/reactor/hubs" | "/[[locale]]/reactor/hubs/[id]" | "/[[locale]]/reactor/[id]" | "/robots.txt" | "/[[locale]]/(index)/rules" | "/sitemap.xml" | "/test-image-input" | "/[[locale]]/test" | "/[[locale]]/test/editor" | "/[[locale]]/test/tag" | "/[[locale]]/(index)/the-law" | "/[[locale]]/(index)" | "/[[locale]]";

export type RouteParams<T extends RouteId> = T extends keyof DynamicRoutes ? DynamicRoutes[T] : Record<string, never>;

export type LayoutParams<T extends RouteId> = Layouts[T] | Record<string, never>;

export type Pathname = "/" | "/admin" | "/admin/invites" | "/api" | `/api/${string}` & {} | `${string}/auth` & {} | `${string}/communes` & {} | `${string}/communes/invitations` & {} | `${string}/communes/join-requests` & {} | `${string}/communes/${string}` & {} | `${string}/communes/${string}/invitations` & {} | `${string}/communes/${string}/join-requests` & {} | `${string}/layout` & {} | `${string}/new-calendar` & {} | `${string}/new-english` & {} | `${string}/people` & {} | `${string}/people/${string}` & {} | `${string}/people/${string}/feedback` & {} | `${string}/people/${string}/karma` & {} | `${string}/profile` & {} | `${string}/reactor` & {} | `${string}/reactor/communities` & {} | `${string}/reactor/communities/${string}` & {} | `${string}/reactor/hubs` & {} | `${string}/reactor/hubs/${string}` & {} | `${string}/reactor/${string}` & {} | "/robots.txt" | `${string}/rules` & {} | "/sitemap.xml" | "/test-image-input" | `${string}/test` & {} | `${string}/test/editor` & {} | `${string}/test/tag` & {} | `${string}/the-law` & {} | `${string}` & {};

export type ResolvedPathname = `${"" | `/${string}`}${Pathname}`;

export type Asset = "/favicon.png" | "/images/alignment-system/kung-fu-panda/chaotic-evil.png" | "/images/alignment-system/kung-fu-panda/chaotic-good.png" | "/images/alignment-system/kung-fu-panda/chaotic-neutral.png" | "/images/alignment-system/kung-fu-panda/lawful-evil.png" | "/images/alignment-system/kung-fu-panda/lawful-good.png" | "/images/alignment-system/kung-fu-panda/lawful-neutral.png" | "/images/alignment-system/kung-fu-panda/neutral-evil.png" | "/images/alignment-system/kung-fu-panda/neutral-good.png" | "/images/alignment-system/kung-fu-panda/true-neutral.png" | "/images/default-avatar.png" | "/images/full-v3-transparent-white.svg" | "/images/full-v3-transparent.svg" | "/images/hi-chat.gif" | "/images/home-page-main-image-mobile.png" | "/images/home-page-main-image.png" | "/tinymce/icons/default/icons.js" | "/tinymce/icons/default/icons.min.js" | "/tinymce/icons/default/index.js" | "/tinymce/models/dom/index.js" | "/tinymce/models/dom/model.js" | "/tinymce/models/dom/model.min.js" | "/tinymce/plugins/image/index.js" | "/tinymce/plugins/image/plugin.js" | "/tinymce/plugins/image/plugin.min.js" | "/tinymce/plugins/lists/index.js" | "/tinymce/plugins/lists/plugin.js" | "/tinymce/plugins/lists/plugin.min.js" | "/tinymce/skins/content/dark/content.css" | "/tinymce/skins/content/dark/content.js" | "/tinymce/skins/content/dark/content.min.css" | "/tinymce/skins/content/default/content.css" | "/tinymce/skins/content/default/content.js" | "/tinymce/skins/content/default/content.min.css" | "/tinymce/skins/content/document/content.css" | "/tinymce/skins/content/document/content.js" | "/tinymce/skins/content/document/content.min.css" | "/tinymce/skins/content/tinymce-5/content.css" | "/tinymce/skins/content/tinymce-5/content.js" | "/tinymce/skins/content/tinymce-5/content.min.css" | "/tinymce/skins/content/tinymce-5-dark/content.css" | "/tinymce/skins/content/tinymce-5-dark/content.js" | "/tinymce/skins/content/tinymce-5-dark/content.min.css" | "/tinymce/skins/content/writer/content.css" | "/tinymce/skins/content/writer/content.js" | "/tinymce/skins/content/writer/content.min.css" | "/tinymce/skins/ui/oxide/content.css" | "/tinymce/skins/ui/oxide/content.inline.css" | "/tinymce/skins/ui/oxide/content.inline.js" | "/tinymce/skins/ui/oxide/content.inline.min.css" | "/tinymce/skins/ui/oxide/content.js" | "/tinymce/skins/ui/oxide/content.min.css" | "/tinymce/skins/ui/oxide/skin.css" | "/tinymce/skins/ui/oxide/skin.js" | "/tinymce/skins/ui/oxide/skin.min.css" | "/tinymce/skins/ui/oxide/skin.shadowdom.css" | "/tinymce/skins/ui/oxide/skin.shadowdom.js" | "/tinymce/skins/ui/oxide/skin.shadowdom.min.css" | "/tinymce/skins/ui/oxide-dark/content.css" | "/tinymce/skins/ui/oxide-dark/content.inline.css" | "/tinymce/skins/ui/oxide-dark/content.inline.js" | "/tinymce/skins/ui/oxide-dark/content.inline.min.css" | "/tinymce/skins/ui/oxide-dark/content.js" | "/tinymce/skins/ui/oxide-dark/content.min.css" | "/tinymce/skins/ui/oxide-dark/skin.css" | "/tinymce/skins/ui/oxide-dark/skin.js" | "/tinymce/skins/ui/oxide-dark/skin.min.css" | "/tinymce/skins/ui/oxide-dark/skin.shadowdom.css" | "/tinymce/skins/ui/oxide-dark/skin.shadowdom.js" | "/tinymce/skins/ui/oxide-dark/skin.shadowdom.min.css" | "/tinymce/skins/ui/tinymce-5/content.css" | "/tinymce/skins/ui/tinymce-5/content.inline.css" | "/tinymce/skins/ui/tinymce-5/content.inline.js" | "/tinymce/skins/ui/tinymce-5/content.inline.min.css" | "/tinymce/skins/ui/tinymce-5/content.js" | "/tinymce/skins/ui/tinymce-5/content.min.css" | "/tinymce/skins/ui/tinymce-5/skin.css" | "/tinymce/skins/ui/tinymce-5/skin.js" | "/tinymce/skins/ui/tinymce-5/skin.min.css" | "/tinymce/skins/ui/tinymce-5/skin.shadowdom.css" | "/tinymce/skins/ui/tinymce-5/skin.shadowdom.js" | "/tinymce/skins/ui/tinymce-5/skin.shadowdom.min.css" | "/tinymce/skins/ui/tinymce-5-dark/content.css" | "/tinymce/skins/ui/tinymce-5-dark/content.inline.css" | "/tinymce/skins/ui/tinymce-5-dark/content.inline.js" | "/tinymce/skins/ui/tinymce-5-dark/content.inline.min.css" | "/tinymce/skins/ui/tinymce-5-dark/content.js" | "/tinymce/skins/ui/tinymce-5-dark/content.min.css" | "/tinymce/skins/ui/tinymce-5-dark/skin.css" | "/tinymce/skins/ui/tinymce-5-dark/skin.js" | "/tinymce/skins/ui/tinymce-5-dark/skin.min.css" | "/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.css" | "/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.js" | "/tinymce/skins/ui/tinymce-5-dark/skin.shadowdom.min.css" | "/tinymce/themes/silver/index.js" | "/tinymce/themes/silver/theme.js" | "/tinymce/themes/silver/theme.min.js" | "/tinymce/tinymce.min.js";